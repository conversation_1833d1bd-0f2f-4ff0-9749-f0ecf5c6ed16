<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跨域测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>跨域配置测试页面</h1>
    
    <div class="test-section">
        <h3>测试 System API - 字典接口</h3>
        <button onclick="testSystemAPI()">测试获取所有字典</button>
        <button onclick="testSystemAPIByCode()">测试根据Code获取字典</button>
        <div id="systemResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>测试 Content API - 课程接口</h3>
        <button onclick="testContentAPI()">测试课程列表</button>
        <div id="contentResult" class="result"></div>
    </div>

    <script>
        // 测试 System API
        async function testSystemAPI() {
            const resultDiv = document.getElementById('systemResult');
            resultDiv.textContent = '正在请求...';
            
            try {
                const response = await fetch('http://localhost:63110/system/dictionary/all', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 请求成功！\n状态码: ${response.status}\n数据: ${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 请求失败！\n状态码: ${response.status}\n状态文本: ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求错误！\n错误信息: ${error.message}`;
            }
        }
        
        // 测试 System API - 根据Code获取
        async function testSystemAPIByCode() {
            const resultDiv = document.getElementById('systemResult');
            resultDiv.textContent = '正在请求...';
            
            try {
                const response = await fetch('http://localhost:63110/system/dictionary/code/200001', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 请求成功！\n状态码: ${response.status}\n数据: ${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 请求失败！\n状态码: ${response.status}\n状态文本: ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求错误！\n错误信息: ${error.message}`;
            }
        }
        
        // 测试 Content API
        async function testContentAPI() {
            const resultDiv = document.getElementById('contentResult');
            resultDiv.textContent = '正在请求...';
            
            try {
                const response = await fetch('http://localhost:63040/content/course/list', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        pageNo: 1,
                        pageSize: 10
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 请求成功！\n状态码: ${response.status}\n数据: ${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 请求失败！\n状态码: ${response.status}\n状态文本: ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求错误！\n错误信息: ${error.message}`;
            }
        }
    </script>
</body>
</html>
